第一层公式是:=IFERROR(IF('轿厢>井道'!G141,"因对重框架限制，轿厢深度最小值 "&'轿厢>井道'!E141&"mm，请调整；",""),"")
G141的公式是:=OR(G138,G139)
E141的公式是:=IF(G141,ROUNDUP(MAX(2*ABS(IF(G138,(-B138+D138),0)),IF(AND(C20,G139),2*ABS((B139-D139)),0))/50,0)*50,0)+B17
B17的公式是：=Home!E12（代表着既定参数Car Depth）
B138的公式是:=IFERROR(E115+B56-25,"-")
E115的公式是：=IF(C15,"-",B113-B57/2-B112-B56)
B57的公式是：=OFFSET(J13,0,$G$12,1,1)
$G$12的公式是：=MATCH(B12,K12:Q12,0)
K12的公式是：
B12的公式是：=Home!C9（代表着既定参数 Lift Model）
J13的公式是：好像是个字符串 CWG
C15的公式是：=IF(B15="REAR",TRUE,FALSE)
B113的公式是：=B69
B69的公式是：=OFFSET(J15,0,G12,1,1)
J15的公式是：好像是一个字符串 CWe
G12的公式是：好像是一个字符串 梯型序号
B56的公式是：=IF(Home!$I$18="Critical",0,OFFSET(IF(Home!$I$18="Well to do",井道尺寸数据!A4,井道尺寸数据!A3),0,IF(B14<=50,1,IF(B14<=125,2,3))))
B139的公式是:=IFERROR(E116-B56+25,"-")
D138的公式是:=-G106-B99
D139的公式是:=IF(C20,B17-G106+B99,"-")
C20的公式是:=Home!I15(代表着既定参数Through Door)
G138的公式是:=IF(C138="≥",FALSE,TRUE)
G139的公式是:=IF(C139="≤",FALSE,TRUE)
E115的公式是：=IF(C15,"-",B113-B57/2-B112-B56)





